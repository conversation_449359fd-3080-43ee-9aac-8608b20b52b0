import { useEffect, useCallback } from 'react';

// 定义一个常量存储 ESC 键的键值，提高代码可读性
const ESCAPE_KEY = 'Escape';

export default function useEscape(onCancel: () => void) {
  // 使用 useCallback 缓存事件处理函数，避免每次渲染都创建新的函数
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      // 直接比较键值，避免正则表达式带来的性能开销
      if (e.key === ESCAPE_KEY && typeof onCancel === 'function') {
        e.preventDefault();
        e.stopPropagation();
        onCancel();
      }
    },
    [onCancel]
  );

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown, true);
    return () => {
      document.removeEventListener('keydown', handleKeyDown, true);
    };
  }, [handleKeyDown]);
}
