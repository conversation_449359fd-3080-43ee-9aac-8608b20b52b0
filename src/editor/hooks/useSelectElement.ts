import { useEffect, useState } from 'react';
// 假设这些外部依赖项已经有类型定义，或者我们可以为它们创建占位符
import {
  generateSelector,
  getElementAttributes,
  getParentPadding,
  evaluateSelectorAll,
  type ElementAttributes,
  type ParentPadding,
} from '../utils/selector';

// --- 类型定义 ---

// 定义 useSelectElement hook 的选项
export interface UseSelectElementOptions {
  /**
   * 定义一个元素被视为“通用容器”的最大子元素数量。
   * 如果元素的后代总数超过此值，它将被忽略。
   * 设置为 null 或 undefined 可以禁用此检查。
   * @default 100
   */
  maxChildren?: number | null;
}

// 定义悬停元素的状态结构
export interface HoveredElementState {
  element: Element;
  selector: string;
  padding: ParentPadding;
}

// 定义选中元素后，通过回调函数传递的数据结构
export interface SelectedElementPayload {
  selector: string | null;
  element: Element;
  elementAttributes: ElementAttributes;
  clientX: number;
  clientY: number;
  padding: ParentPadding;
}

// --- 辅助函数 (已添加类型) ---

const DISABLED_ELEMENTS = new Set(['HTML', 'BODY']);

function getParent(node: Node): ParentNode | null {
  return node.nodeType === Node.DOCUMENT_FRAGMENT_NODE
    ? (node as ShadowRoot).host
    : node.parentNode;
}

function canAccessIframe(iframe: HTMLIFrameElement): boolean {
  try {
    // 使用 `?.` 操作符来更安全地访问，并明确转换为布尔值
    return !!iframe.contentDocument;
  } catch {
    return false;
  }
}

export const setValidTarget = (
  elementTree: Element[]
): Element[] | null => {
  if (elementTree.length === 0) return null;

  const [target] = elementTree;
  if (target.tagName === 'SLOT') {
    return setValidTarget(elementTree.slice(1));
  }
  return elementTree;
};

export function getAncestorTree(element: Element): Element[] | null {
  const tree: Element[] = [element];
  let parent = getParent(element);
  // 确保 parent 是一个 Element 类型的节点
  while (parent && parent instanceof Element) {
    tree.push(parent);
    parent = getParent(parent);
  }
  return setValidTarget(tree);
}

function reTargetSvgElements(tree: Element[]): Element[] {
  const [target] = tree;
  // SVGElement 继承自 Element，但有 .closest 方法
  const svgAncestor = target.closest('svg');
  if (svgAncestor && svgAncestor.parentElement) {
    // 如果找到了 SVG 祖先，并且它有父元素，则从父元素开始截取
    const parentIndex = tree.indexOf(svgAncestor.parentElement);
    if (parentIndex > -1) {
      return tree.slice(parentIndex);
    }
  }
  return tree;
}

const getShadowRootIterator = (root: Node): NodeIterator => {
  return document.createNodeIterator(root, NodeFilter.SHOW_ELEMENT, node => {
    // 明确类型守卫
    if (!(node instanceof Element)) return NodeFilter.FILTER_REJECT;

    // 排除FLOWEZ相关的自定义元素
    const isExcluded = ['FLOWEZ-BUILDER'].includes(node.nodeName);

    return node.shadowRoot && !isExcluded
      ? NodeFilter.FILTER_ACCEPT
      : NodeFilter.FILTER_REJECT;
  });
};

const getIframeIterator = (root: Node): NodeIterator => {
  return document.createNodeIterator(root, NodeFilter.SHOW_ELEMENT, node => {
    return node.nodeName === 'IFRAME'
      ? NodeFilter.FILTER_ACCEPT
      : NodeFilter.FILTER_REJECT;
  });
};

function forEachIframe(root: Node, callback: (iframe: HTMLIFrameElement) => void) {
  const iterator = getIframeIterator(root);
  let currentNode: Node | null;
  while ((currentNode = iterator.nextNode())) {
    // 类型守卫
    if (currentNode instanceof HTMLIFrameElement && canAccessIframe(currentNode)) {
      callback(currentNode);
    }
  }
}

function forEachShadowRoot(root: Node, callback: (shadowRoot: ShadowRoot) => void) {
  const traverse = (iterator: NodeIterator) => {
    let currentNode: Node | null;
    while ((currentNode = iterator.nextNode())) {
      // 类型守卫确保我们处理的是带有 shadowRoot 的元素
      if (currentNode instanceof Element && currentNode.shadowRoot) {
        callback(currentNode.shadowRoot);
        // 递归遍历子级的 shadow roots
        const childIterator = getShadowRootIterator(currentNode.shadowRoot);
        traverse(childIterator);
      }
    }
  };
  traverse(getShadowRootIterator(root));
}

/**
 * 检查一个元素是否是“通用”的（即，一个包含大量后代的容器）。
 * 这是一个广度优先搜索 (BFS)。
 * @param target 要检查的元素
 * @param max 允许的最大后代总数，超过则视为通用元素。
 * @returns 如果是通用元素则返回 true，否则返回 false。
 */
export function isGenericElement(target: Element, max: number | null | undefined): boolean {
  if (max === null || max === undefined) {
    return false;
  }

  if (target.children.length === 0) {
    return false;
  }

  let totalChildren = 0;
  // 使用 Element[] 类型可以更好地利用类型推断
  const queue: Element[] = [target];
  let head = 0; // 使用索引代替 shift() 以提高性能

  // 这是一个优化的广度优先搜索 (BFS)
  while (head < queue.length) {
    const element = queue[head++]; // Dequeue
    const children = Array.from(element.children);
    totalChildren += children.length;

    if (totalChildren >= max) {
      return true;
    }

    // Enqueue children
    for (const child of children) {
        queue.push(child);
    }
  }

  return false;
}

// --- 主 Hook ---

/**
 * 一个 React Hook，用于启用一个“元素选择”模式。
 * 它可以高亮用户悬停的元素，并在用户点击时捕获该元素并生成唯一的CSS选择器。
 *
 * @param onSelect 当用户点击并选择一个有效元素时触发的回调函数。
 *   **重要提示:** 请使用 `useCallback` 来包裹此函数以避免不必要的 effect 重启。
 * @param options 配置选项，例如 `maxChildren`。
 * @returns 返回当前悬停的元素信息对象，如果没有则返回 `null`。
 */
export default function useSelectElement(
  onSelect: (payload: SelectedElementPayload) => void,
  options: UseSelectElementOptions = {}
): HoveredElementState | null {
  const [hoveredElement, setHoveredElement] = useState<HoveredElementState | null>(null);
  const { maxChildren = 100 } = options;

  useEffect(() => {
    const handleClick = (e: Event) => {
      if (!(e instanceof MouseEvent)) return;

      // 检查 Shift 键是否被按下
      if (e.shiftKey) return;

      // 使用 composedPath 获取 Shadow DOM 内的真实目标
      const target = e.composedPath()[0];
      if (!(target instanceof Element)) return;

      const initialTree = getAncestorTree(target);
      if (!initialTree) return;

      // 阻止事件的默认行为和传播
      e.preventDefault();
      e.stopImmediatePropagation();

      const [initialTarget] = initialTree;
      if (
        isGenericElement(initialTarget, maxChildren) ||
        DISABLED_ELEMENTS.has(initialTarget.tagName)
      ) {
        return;
      }

      const tree = reTargetSvgElements(initialTree);
      const [finalTarget] = tree;
      const padding = getParentPadding(finalTarget);
      const selector = generateSelector(tree);
      const { clientX, clientY } = e;

      // 验证选择器的唯一性
      if (selector && evaluateSelectorAll(selector).length === 1) {
        onSelect({
          selector,
          element: finalTarget,
          elementAttributes: getElementAttributes(finalTarget),
          clientX: clientX + (padding?.left ?? 0),
          clientY: clientY + (padding?.top ?? 0),
          padding,
        });
      } else {
        // 即使无法生成唯一选择器，也可能需要通知上层
        onSelect({
          selector: null,
          element: finalTarget,
          elementAttributes: getElementAttributes(finalTarget),
          clientX,
          clientY,
          padding,
        });
      }
    };

    const cancelEvent = (e: MouseEvent) => {
      if (e.shiftKey) return;
      e.preventDefault();
      e.stopImmediatePropagation();
    };

    // 在捕获阶段添加监听器
    document.addEventListener('mousedown', cancelEvent, true);
    document.addEventListener('mouseup', cancelEvent, true);
    document.addEventListener('click', handleClick, true);
    forEachIframe(document.body, iframe => {
      iframe.contentWindow?.document.addEventListener('mousedown', cancelEvent, true);
      iframe.contentWindow?.document.addEventListener('mouseup', cancelEvent, true);
      iframe.contentWindow?.document.addEventListener('click', handleClick, true);
    });

    return () => {
      document.removeEventListener('mousedown', cancelEvent, true);
      document.removeEventListener('mouseup', cancelEvent, true);
      document.removeEventListener('click', handleClick, true);
      forEachIframe(document.body, iframe => {
        iframe.contentWindow?.document.removeEventListener('mousedown', cancelEvent, true);
        iframe.contentWindow?.document.removeEventListener('mouseup', cancelEvent, true);
        iframe.contentWindow?.document.removeEventListener('click', handleClick, true);
      });
    };
  }, [maxChildren, onSelect]);

  useEffect(() => {
    const handleMouseOver = (e: Event) => {
      if (!(e instanceof MouseEvent)) return;

      if (e.shiftKey) {
        setHoveredElement(null);
        return;
      }

      const target = e.composedPath()[0];
      if (!(target instanceof Element)) return;

      const initialTree = getAncestorTree(target);
      if (!initialTree) return;

      const [initialTarget] = initialTree;
      if (
        isGenericElement(initialTarget, maxChildren) ||
        DISABLED_ELEMENTS.has(initialTarget.tagName)
      ) {
        setHoveredElement(null);
        return;
      }

      const tree = reTargetSvgElements(initialTree);
      const [finalTarget] = tree;

      setHoveredElement({
        element: finalTarget,
        selector: generateSelector(tree) ?? '',
        padding: getParentPadding(finalTarget),
      });
    };

    document.body.addEventListener('mouseover', handleMouseOver, true);
    forEachShadowRoot(document.body, shadowRoot => {
      shadowRoot.addEventListener('mouseover', handleMouseOver, true);
    });
    forEachIframe(document.body, iframe => {
      iframe.contentWindow?.document.addEventListener('mouseover', handleMouseOver, true);
    });

    return () => {
      document.body.removeEventListener('mouseover', handleMouseOver, true);
      forEachShadowRoot(document.body, shadowRoot =>
        shadowRoot.removeEventListener('mouseover', handleMouseOver, true)
      );
      forEachIframe(document.body, iframe => {
        iframe.contentWindow?.document.removeEventListener('mouseover', handleMouseOver, true);
      });
    };
  }, [maxChildren]);

  useEffect(() => {
    const handleMouseLeave = () => {
        setHoveredElement(null);
    };

    const currentElement = hoveredElement?.element;
    if (currentElement) {
        currentElement.addEventListener('mouseleave', handleMouseLeave, { once: true });
    }

    return () => {
        if (currentElement) {
            currentElement.removeEventListener('mouseleave', handleMouseLeave);
        }
    };
  }, [hoveredElement]);

  return hoveredElement;
}