import { useCallback, useEffect, useState } from 'react';
import debounce from 'lodash/debounce';
import isEqual from 'lodash/isEqual';

/**
 * Hook to recalculate highlighted element positions on scroll and resize
 *
 * @typedef {object} Element
 * @property {DOMElement} element - DOM element to highlight
 * @property {string} [label] - Optional selector label
 *
 * @param {Element[]} elements - Elements to be highlighted
 * @return {Position[]} Position dimensions to highlight
 */
export default function useHighlights(elements: any) {
  const [positions, setPositions] = useState([]);

  // Callback to execute on mount and resize/scroll to calculate the highlight
  // box dimensions for each element provided.
  const reposition = useCallback(() => {
    const updated = elements.filter(Boolean).map(({ element, label, padding }) => {
      const { top, left, height, width } = element.getBoundingClientRect();
      return {
        label,
        top: top + (padding?.top || 0),
        left: left + (padding?.left || 0),
        height,
        width,
      };
    });

    // If the calculated positions have not changed, do not trigger a state
    // update and potentially cause an infinite loop
    if (!isEqual(updated, positions)) {
      setPositions(updated);
    }
  }, [elements, positions]);

  useEffect(() => {
    // Immediately execute the callback on mount for initial highlights
    reposition();

    // Debounce the handler to limit calls during scroll/resize actions
    const handler = debounce(reposition, 100);

    window.addEventListener('resize', handler);
    window.addEventListener('scroll', handler);

    return () => {
      window.removeEventListener('resize', handler);
      window.removeEventListener('scroll', handler);
    };
  }, [reposition]);

  return positions;
}
