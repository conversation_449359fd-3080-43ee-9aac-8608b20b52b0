import React from 'react';
import { createRoot } from 'react-dom/client';

import { ConfigProvider } from '@/components';
import { StyleProvider } from '@ant-design/cssinjs';

import BuildRoot from './components/builder-root';
import { initShiftListener } from '../utils/event';

export default class Editor {
  private options!: InitSDKOptions;

  private getAllShadowRoots(): ShadowRoot[] {
    const shadowRoots: ShadowRoot[] = [];
    const traverse = (node: Node) => {
      if (node instanceof Element && node.shadowRoot) {
        shadowRoots.push(node.shadowRoot);
        traverse(node.shadowRoot);
      }
      for (let i = 0; i < node.childNodes.length; i++) {
        traverse(node.childNodes[i]);
      }
    };
    traverse(document.documentElement);
    return shadowRoots;
  }

  preventEventBubbling(shadowRoot: ShadowRoot) {
    const bubbleEvents = [
      'click',
      'dblclick',
      'mousedown',
      'mouseup',
      'dragstart',
      'dragenter',
      'dragleave',
      'dragover',
      'drop',
      'dragend',
      'keydown',
      'keypress',
      'keyup',
      'select',
      'change',
      'submit',
      'focusin',
    ];

    // 获取文档中所有的 shadowRoot
    const allShadowRoots = this.getAllShadowRoots();

    allShadowRoots.forEach((otherShadowRoot) => {
      // 排除当前的 shadowRoot
      if (otherShadowRoot !== shadowRoot) {
        bubbleEvents.forEach((eventName) => {
          otherShadowRoot.addEventListener(eventName, (event) => {
            event.stopPropagation();
          });
        });
      }
    });
  }

  renderEditor() {
    if (document.querySelectorAll('flowez-builder').length === 0) {
      // 创建编辑器容器
      const builderContainer: HTMLElement = document.createElement('flowez-builder');
      builderContainer.setAttribute('id', 'editor-builder');

      // 挂载编辑器
      const body = document.querySelector('body');
      body?.appendChild(builderContainer);

      const shadowRoot = builderContainer.attachShadow({ mode: 'open' });

      // 监听 shift
      initShiftListener();
      // 防止事件冒泡
      this.preventEventBubbling(shadowRoot);

      const root = createRoot(shadowRoot);
      root.render(
        <StyleProvider container={shadowRoot}>
          <ConfigProvider
            prefixCls='flowez'
            theme={{
              token: {
                colorPrimary: '#406cf6',
                colorBgContainer: '#424b5e',
                colorText: '#fff',
                colorTextPlaceholder: '#fff',
                controlOutline: 'transparent',
                colorBgElevated: '#424b5e',
              },
              components: {
                Button: {
                  defaultActiveBorderColor: 'transparent',
                  defaultBorderColor: 'transparent',
                  defaultHoverBorderColor: 'transparent',
                },
                Input: {
                  activeBorderColor: '#424b5e',
                  hoverBorderColor: '#424b5e',
                  colorBorder: '#424b5e',
                },
                Menu: {
                  itemHoverBg: '#657290',
                  colorSplit: 'transparent',
                },
              },
            }}>
            <BuildRoot options={this.options} />
          </ConfigProvider>
        </StyleProvider>
      );
    }
  }

  // 初始化方法
  init(options: InitSDKOptions) {
    this.options = options;
    if (document.readyState !== 'loading') {
      this.renderEditor();
    } else {
      document.addEventListener('DOMContentLoaded', this.renderEditor);
    }
  }
}
