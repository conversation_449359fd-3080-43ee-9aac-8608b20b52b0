import { StepTypeEnum } from './utils/enum';

interface Step {
  stepId: string;
  stepType: StepTypeEnum | undefined;
  orderSeq: number;
  color?: string;
  radius?: number;
  closeStep?: string;
  location?: string;
  locationType?: string;
  locationCustom?: string;
  customLocation?: string;
  textLocation?: string;
  pageRange?: string;
  userRange?: string;
  creationDate?: string;
}

interface Flow {
  flowId: string;
  flowName: string;
  steps: Step[];
}

interface StepAttrs {
  [key: string]: any;
}

interface Status {
  [key: string]: any;
}

export type { Flow, Step, StepAttrs, Status };
