/**
 * 检查元素是否被排除
 * @param e - 事件对象
 * @returns 如果元素被排除则返回 true，否则返回 false
 */
const checkIfExcludedElement = (e: { target: Element | EventTarget | null }) => {
  return (e.target as Element)?.closest?.('[id^="editor-builder"]');
};

/**
 * 当悬停在svg元素上时，我们使用svg的parentElement作为选择器，因为
 * 1) svg元素的 classname 是一个对象而不是字符串 - 会影响我们的选择器方法（使用 classname）
 * 2) svg元素的 width/height 只在 viewBox 内有效，不能代表元素的实际 width/height
 */
const filterSvgElement = (ele: any) => {
  let current = ele;
  while (typeof current.className !== 'string') {
    current = current.parentElement;
  }
  return current;
};

const getBuilderRoot = () => {
  const builderTagName = 'flowez-builder';
  const $builder = document.querySelector(builderTagName);
  return $builder!.shadowRoot;
};

const getEditorRoot = ($builder = getBuilderRoot()) => {
  return $builder!.querySelector('#editor-root');
};

const getPortalRoot = ($builder = getBuilderRoot()) => {
  return $builder!.querySelector('#portal-root');
};

const getMaxZIndex = () => {
  return Array.from(document.querySelectorAll('*')).reduce((maxZIndex, element) => {
    const zIndex = window.getComputedStyle(element).zIndex;
    // 排除 flowez-builder 元素下的 DOM
    if (zIndex !== 'auto' && !element.closest('flowez-builder')) {
      return Math.max(maxZIndex, parseInt(zIndex, 10) || 0);
    }
    return maxZIndex;
  }, 0);
};

export { checkIfExcludedElement, filterSvgElement, getBuilderRoot, getEditorRoot, getPortalRoot, getMaxZIndex };
