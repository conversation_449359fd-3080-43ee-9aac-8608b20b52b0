import uniqueSelector from '@flow-ez/unique-selector';

export type ElementStack = Array<Element | ShadowRoot | Document | Node>;

export interface ElementAttributes {
  tagName: string;
  class?: string[];
  [key: string]: string | string[] | undefined;
}

export interface ParentPadding {
  top: number;
  left: number;
  right: number;
  bottom: number;
}

// 常量定义
const SHADOW_ROOT_TOKEN = '|shadow-root|';
const IFRAME_ROOT_TOKEN = '|iframe|';

// 正则表达式模式 - 用于识别不稳定的选择器
// const DIGITS = /\d{2,}/; // many digits in a class/attribute is often brittle
// const EMBER = /(ember(\d+))/; // generated IDs/attributes for ember components
// const FLOWEZ = /(flowez)/; // don't capture anything to do with flowez
// const ANGULAR = /(ng-.+)/; // angular class/attribute names are often dynamic
// const GCP = /GCP/; // GCP class names are often dynamic
// const STYLED_COMPONENT = /(sc-.+)/; // Styled-component classes change often
// const CLASS_ATTR = /^(class)$/; // don't treat the whole class string as an attr
// const BRACKET = /{/; // {'s often mean dynamic values being inserted/replaced
// const EMPTY = /^\s+$/;

// 模式数组 - 用于过滤不稳定的选择器
// const ID_PATTERNS = [DIGITS, EMBER];
// const CLASS_PATTERNS = [DIGITS, FLOWEZ, ANGULAR, GCP, STYLED_COMPONENT, EMPTY];
// const ATTRIBUTE_NAME_PATTERNS = [ANGULAR, CLASS_ATTR];
// const ATTRIBUTE_VALUE_PATTERNS = [DIGITS, FLOWEZ, EMBER, BRACKET];

/**
 * 生成元素的唯一选择器，过滤掉不稳定的属性
 * @param element - 目标元素
 * @param opts - 选择器生成选项
 * @returns 生成的CSS选择器字符串
 */
function selectElement(element: Element): string | null {
  try {
    return uniqueSelector(element, {
      excludeRegex: /hover|active|focus/,
    });
  } catch (error) {
    console.warn('Failed to generate unique selector:', error);
    // 降级方案：使用基本的标签名选择器
    return element.tagName.toLowerCase();
  }
}

/**
 * 递归生成元素的选择器，支持 Shadow DOM 和 iframe
 * @param stack - 元素堆栈，包含从目标元素到根元素的路径
 * @param selector - 当前累积的选择器字符串
 * @returns 完整的CSS选择器字符串
 */
export function generateSelector(stack: ElementStack, selector: string = ''): string {
  if (!stack || stack.length === 0) {
    return selector.trim();
  }

  const [$target] = stack;

  if (!$target || !($target instanceof Element)) {
    return selector.trim();
  }

  try {
    const $root = $target.getRootNode();

    // 处理普通 DOM 元素
    if ($root === document) {
      const elementSelector = selectElement($target);
      return `${elementSelector} ${selector}`.trim();
    }

    // 处理 Shadow DOM
    if ($root instanceof ShadowRoot) {
      const shadowRootIndex = stack.indexOf($root);
      if (shadowRootIndex === -1 || shadowRootIndex === 0) {
        return selector.trim();
      }

      const rootElement = stack[shadowRootIndex - 1];
      if (!(rootElement instanceof Element)) {
        return selector.trim();
      }

      return generateSelector(
        stack.slice(shadowRootIndex + 1),
        `${SHADOW_ROOT_TOKEN} ${selectElement($target)} ${selector}`
      );
    }

    // 处理 iframe
    const currentWindow = ($root as any).parentWindow || ($root as any).defaultView;
    const isIframe = currentWindow && currentWindow !== window;

    if (isIframe && currentWindow?.frameElement) {
      const iframeRootIndex = stack.indexOf($root);
      if (iframeRootIndex === -1 || iframeRootIndex === 0) {
        return selector.trim();
      }

      const rootElement = stack[iframeRootIndex - 1];
      if (!(rootElement instanceof Element)) {
        return selector.trim();
      }

      return generateSelector(
        stack.slice(iframeRootIndex + 1),
        `${selectElement(currentWindow.frameElement)} ${IFRAME_ROOT_TOKEN} ${selectElement($target)} ${selector}`
      );
    }

    return selector.trim();
  } catch (error) {
    console.warn('Error generating selector:', error);
    return selector.trim();
  }
}

/**
 * 获取元素的允许属性
 * @param element - 目标元素
 * @returns 包含允许属性的对象
 */
function allowedAttributes(element: Element): Record<string, string> {
  const allowedAttrs = ['href', 'src', 'alt', 'title', 'type', 'name', 'placeholder', 'id'];

  return allowedAttrs.reduce((acc: Record<string, string>, attrName: string) => {
    const val = element.getAttribute(attrName);
    if (val) {
      acc[attrName] = val;
    }
    return acc;
  }, {});
}

/**
 * 获取元素的所有相关属性
 * @param element - 目标元素
 * @returns 包含元素属性的对象
 */
export function getElementAttributes(element: Element): ElementAttributes {
  try {
    return {
      tagName: element.tagName,
      ...(element.classList.length > 0 ? { class: Array.from(element.classList) } : {}),
      ...allowedAttributes(element),
    };
  } catch (error) {
    console.warn('Error getting element attributes:', error);
    return {
      tagName: element.tagName || 'UNKNOWN',
    };
  }
}

/**
 * 获取元素的父级填充信息，主要用于 iframe 场景
 * @param element - 目标元素
 * @returns 包含 top 和 left 偏移量的对象
 */
export function getParentPadding(element: Element): ParentPadding {
  try {
    const $root = element.getRootNode();
    const currentWindow = ($root as any).parentWindow || ($root as any).defaultView;
    const isIframe = currentWindow && currentWindow !== window;

    if (isIframe && currentWindow?.frameElement) {
      const frameElement = currentWindow.frameElement as HTMLIFrameElement;
      const rect = frameElement.getBoundingClientRect();
      return {
        top: rect.top,
        left: rect.left,
        right: rect.right,
        bottom: rect.bottom,
      };
    }

    return {
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    };
  } catch (error) {
    console.warn('Error getting parent padding:', error);
    return {
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    };
  }
}

/**
 * 评估选择器并返回匹配的元素，支持 Shadow DOM 和 iframe
 * @param selector - CSS选择器字符串
 * @param context - 查询上下文，默认为 document
 * @returns 匹配的元素数组或 NodeList
 */
export const evaluateSelectorAll = (
  selector: string,
  context: Document | Element = document
): Element[] | NodeListOf<Element> => {
  try {
    if (!selector || typeof selector !== 'string') {
      return [];
    }

    // 处理包含 Shadow DOM 或 iframe 标记的选择器
    if (selector.includes(SHADOW_ROOT_TOKEN) || selector.includes(IFRAME_ROOT_TOKEN)) {
      const segments = selector
        .split(SHADOW_ROOT_TOKEN)
        .flatMap((sel: string) => sel.split(IFRAME_ROOT_TOKEN))
        .map((seg) => seg.trim())
        .filter((seg) => seg.length > 0);

      let elements: Element[] | null = null;

      for (const selectorSegment of segments) {
        if (elements === null) {
          elements = Array.from(context.querySelectorAll(selectorSegment));
        } else {
          const newElements: Element[] = [];
          for (const el of elements) {
            try {
              // 处理 Shadow DOM
              if ((el as any).shadowRoot) {
                const shadowElements = Array.from(
                  (el as any).shadowRoot.querySelectorAll(selectorSegment)
                ) as Element[];
                newElements.push(...shadowElements);
              }
              // 处理 iframe
              else if (el.tagName === 'IFRAME') {
                const iframe = el as HTMLIFrameElement;
                if (iframe.contentDocument) {
                  const iframeElements = Array.from(
                    iframe.contentDocument.querySelectorAll(selectorSegment)
                  ) as Element[];
                  newElements.push(...iframeElements);
                }
              }
            } catch (error) {
              console.warn('Error querying in shadow root or iframe:', error);
            }
          }
          elements = newElements;
        }
      }

      return elements || [];
    }

    // 处理普通选择器
    return context.querySelectorAll(selector);
  } catch (error) {
    console.warn('Error evaluating selector:', error);
    return [];
  }
};
