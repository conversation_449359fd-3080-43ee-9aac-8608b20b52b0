import React, { useEffect, useRef } from 'react';
import { createRoot, Root } from 'react-dom/client';

interface FrameProps {
  children: React.ReactNode;
  style?: React.CSSProperties;
  className?: string;
  initialContent?: string;
  contentDidMount?: () => void;
  contentDidUpdate?: () => void;
}

const Frame: React.FC<FrameProps> = ({ 
  children, 
  style, 
  className, 
  initialContent,
  contentDidMount,
  contentDidUpdate 
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const rootRef = useRef<Root | null>(null);

  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe) return;

    const initializeFrame = () => {
      const doc = iframe.contentDocument || iframe.contentWindow?.document;
      if (!doc) return;

      const defaultContent = `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <style>
              body {
                margin: 0;
                padding: 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
              }
            </style>
          </head>
          <body>
            <div id="frame-root"></div>
          </body>
        </html>
      `;

      const content = initialContent || defaultContent;
      
      // 使用srcdoc而不是doc.write
      if (iframe.srcdoc !== content) {
        iframe.srcdoc = content;
        return;
      }

      const frameRoot = doc.getElementById('frame-root');
      if (frameRoot) {
        if (rootRef.current) {
          rootRef.current.unmount();
        }
        rootRef.current = createRoot(frameRoot);
        rootRef.current.render(<>{children}</>);
        
        contentDidMount?.();
      }
    };

    if (iframe.contentDocument?.readyState === 'complete') {
      initializeFrame();
    } else {
      iframe.addEventListener('load', initializeFrame);
    }

    return () => {
      iframe.removeEventListener('load', initializeFrame);
      if (rootRef.current) {
        rootRef.current.unmount();
        rootRef.current = null;
      }
    };
  }, [children, initialContent]);

  useEffect(() => {
    if (rootRef.current) {
      rootRef.current.render(<>{children}</>);
      contentDidUpdate?.();
    }
  }, [children, contentDidUpdate]);

  return (
    <iframe
      ref={iframeRef}
      style={{
        border: 'none',
        width: '100%',
        height: '100%',
        ...style,
      }}
      className={className}
      src="about:blank"
    />
  );
};

export default Frame;