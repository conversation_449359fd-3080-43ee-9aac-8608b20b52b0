import styled, { css } from 'styled-components';

const HIGHLIGHT_PADDING = 5;

// Relative `top` position of the label when positioned above the element
const LABEL_HEIGHT = 64;

const FloatTips = styled.div`
  pointer-events: none;
  padding: 0 24px;
  position: fixed;
  inset: auto 0 32px;
  margin-left: auto;
  margin-right: auto;
  width: 400px;
  height: 48px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #424b5e;
  border-radius: 6px;
  box-shadow: rgba(0, 0, 0, 0.25) 0 0 12px;
  opacity: 0.95;
  color: rgba(255, 255, 255, 0.7);
`;

const Span = styled.span``;

const Key = styled.span`
  font-weight: bold;
  color: #406cf6;
`;

const Highlight = styled.div<{ $top: number; $left: number; $height: number; $width: number }>`
  pointer-events: none;
  position: fixed;
  border: 2px solid #20e0d6;
  background: rgba(111, 221, 219, 0.75);
  top: ${({ $top }) => $top - HIGHLIGHT_PADDING}px;
  left: ${({ $left }) => $left - HIGHLIGHT_PADDING}px;
  height: ${({ $height }) => $height + HIGHLIGHT_PADDING * 2}px;
  width: ${({ $width }) => $width + HIGHLIGHT_PADDING * 2}px;
`;

const positionAbove = css`
  top: -${LABEL_HEIGHT}px;
`;

const positionBelow = css`
  top: calc(100% + 16px);
`;

const HighlightLabel = styled.div<{ $position: 'above' | 'below' }>`
  padding: 0 24px;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
  background-color: #424b5e;
  width: 800px;
  height: 48px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 6px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.25);
  opacity: 0.95;
  color: rgba(255, 255, 255, 0.7);
  position: relative;
  width: 500px;

  ${({ $position }) => ($position === 'above' ? positionAbove : positionBelow)};
`;

const LabelSpan = styled.span`
  flex-shrink: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

export { HIGHLIGHT_PADDING, LABEL_HEIGHT, FloatTips, Span, Key, Highlight, HighlightLabel, LabelSpan };
