import React from 'react';

import useHighlights from '@/editor/hooks/useHighlights';
import { LABEL_HEIGHT, Highlight, HighlightLabel, LabelSpan } from './styles';

interface Props {
  elements: any[];
}

const HighlightElement: React.FC<Props> = (props: Props) => {
  const { elements = [] } = props;
  const highlights = useHighlights(elements);

  return (
    <>
      {highlights.map(({ top, left, height, width, label }, index) => (
        <Highlight $top={top} $left={left} $height={height} $width={width} key={index}>
          {label && (
            <HighlightLabel $position={top <= LABEL_HEIGHT ? 'below' : 'above'}>
              <LabelSpan>{label}</LabelSpan>
            </HighlightLabel>
          )}
        </Highlight>
      ))}
    </>
  );
};

export default HighlightElement;
