import React, { useCallback, useEffect, useState } from 'react';
import { nanoid } from 'nanoid';
import dayjs from 'dayjs';

import useEscape from '@/editor/hooks/useEscape';
import useSelectElement from '@/editor/hooks/useSelectElement';
import useEditorStore from '@/editor/stores';
import { ModeEnum } from '@/editor/utils/enum';
import { initialStep } from '@/utils/constants';
import HighlightElement from './HighlightElement';
import { FloatTips, Span, Key } from './styles';

const TargetElement: React.FC = () => {
  const { setMode, stepType, steps, step, setStep, updateStep, addStep } = useEditorStore();
  const [isVisible, setIsVisible] = useState<boolean>(true);

  const handleSelect = useCallback((select: any) => {
    if (select.selector) {
      console.log('成功选择元素:', select);
      if (step?.stepId) {
        updateStep({
          ...step,
          location: select.selector,
          customLocation: select.selector,
        });
      } else {
        const orderSeq = steps.length + 1;
        const newStep = {
          ...initialStep,
          stepId: nanoid(),
          stepType: stepType!,
          orderSeq,
          location: select.selector,
          customLocation: select.selector,
          creationDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        };
        setStep(newStep);
        addStep(newStep);
      }
      setMode(ModeEnum.EDIT);
    } else {
      console.warn('无法为元素生成唯一选择器:', select.element);
      alert('这个元素太普通了，无法唯一定位！');
    }
  }, []);

  const hoveredElement: any = useSelectElement(
    handleSelect,
    { maxChildren: 150 } // 可以覆盖默认选项
  );

  const handleEscape = () => {
    setMode(ModeEnum.EDIT);
  };

  useEscape(handleEscape);

  useEffect(() => {
    const onKeyDown = (e: KeyboardEvent) => {
      if (/shift/i.test(e.key)) {
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        setIsVisible(false);
      }
    };

    const onKeyUp = (e: KeyboardEvent) => {
      if (/shift/i.test(e.key)) {
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        setIsVisible(true);
      }
    };

    document.addEventListener('keydown', onKeyDown, true);
    document.addEventListener('keyup', onKeyUp, true);
    return () => {
      document.removeEventListener('keydown', onKeyDown, true);
      document.removeEventListener('keyup', onKeyUp, true);
    };
  }, [setIsVisible]);

  const highlightedElements = hoveredElement
    ? [
        {
          element: hoveredElement.element,
          label: hoveredElement.selector,
          padding: hoveredElement.padding,
        },
      ]
    : [];

  return (
    <>
      <HighlightElement elements={highlightedElements} />
      {isVisible && (
        <FloatTips>
          <Span>
            <Key>Hold Shift &nbsp;</Key>
            to interact with the page
          </Span>
          <Span>
            <Key>Esc &nbsp;</Key>
            to cancel
          </Span>
        </FloatTips>
      )}
    </>
  );
};

export default TargetElement;
