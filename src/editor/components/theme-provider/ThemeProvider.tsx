import React from 'react';
import { DefaultTheme, StyleSheetManager, ThemeProvider as StyledThemeProvider } from 'styled-components';

import { getMaxZIndex } from '../../utils/document';
import { GlobalStyles } from './style';

const ThemeProvider = (props: any) => {
  const { children, target } = props;

  const theme: DefaultTheme = {};
  const maxZIndex = getMaxZIndex();

  return (
    <StyleSheetManager target={target}>
      <StyledThemeProvider theme={theme}>
        <GlobalStyles $maxZIndex={Math.max(maxZIndex, 1000)} />
        {children}
      </StyledThemeProvider>
    </StyleSheetManager>
  );
};

export default ThemeProvider;
