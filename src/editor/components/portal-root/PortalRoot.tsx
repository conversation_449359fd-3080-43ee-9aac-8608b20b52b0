import React, { useEffect, useRef, useCallback } from 'react';
import { ConfigProvider, Popover } from '@/components';
import type { PopoverProps } from '@/components';
import { TooltipPlacement } from '@/components/tooltip';

import useEditorStore from '../../stores';
import TooltipEditor from './components/tooltip';
import { ModeEnum, StepTypeEnum } from '../../utils/enum';
import { initialStep } from '@/utils/constants';

const PortalRoot = () => {
  const { mode, step, stepType } = useEditorStore();
  const { color, location, locationCustom } = step || initialStep;
  const renderRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<ResizeObserver | null>(null);

  const updateElementPosition = useCallback((dom: Element | null) => {
    if (!dom || !renderRef.current) return;

    const boundingRect = dom.getBoundingClientRect();
    const renderElement = renderRef.current;

    requestAnimationFrame(() => {
      renderElement.style.left = '0';
      renderElement.style.top = '0';
      renderElement.style.position = 'fixed';
      renderElement.style.width = `${boundingRect.width}px`;
      renderElement.style.height = `${boundingRect.height}px`;
      renderElement.style.transform = `translate(${boundingRect.left}px, ${boundingRect.top}px)`;
    });
  }, []);

  const handleWindowChange = useCallback(() => {
    if (!location) return;
    const dom = document.querySelector(location);
    updateElementPosition(dom);
  }, [location, updateElementPosition]);

  useEffect(() => {
    if (location) {
      const dom = document.querySelector(location);

      // 立即更新位置
      updateElementPosition(dom);

      // 设置 ResizeObserver 监听目标元素大小变化
      if (dom) {
        observerRef.current = new ResizeObserver(() => {
          updateElementPosition(dom);
        });
        observerRef.current.observe(dom);
      }

      // 监听窗口变化
      window.addEventListener('scroll', handleWindowChange, { passive: true });
      window.addEventListener('resize', handleWindowChange, { passive: true });

      return () => {
        window.removeEventListener('scroll', handleWindowChange);
        window.removeEventListener('resize', handleWindowChange);
        if (observerRef.current) {
          observerRef.current.disconnect();
        }
      };
    }
  }, [location, handleWindowChange, updateElementPosition]);

  useEffect(() => {
    // 初始滚动位置设置
    window.scrollTo({
      top: document.documentElement.clientHeight,
      left: document.documentElement.clientWidth,
      behavior: 'smooth',
    });
  }, []);

  if (!location || mode === ModeEnum.TARGET) {
    return null;
  }

  const popoverProps: PopoverProps = {
    content: stepType === StepTypeEnum.TOOLTIP ? <TooltipEditor /> : <>modal</>,
    open: true,
    overlayInnerStyle: { padding: 0 },
  };
  if (locationCustom) {
    popoverProps.placement = locationCustom as TooltipPlacement;
  }

  return (
    <ConfigProvider
      theme={{
        components: {
          Popover: {
            colorBgElevated: color,
          },
        },
      }}
    >
      <Popover {...popoverProps}>
        <div ref={renderRef} />
      </Popover>
    </ConfigProvider>
  );
};

export default PortalRoot;
