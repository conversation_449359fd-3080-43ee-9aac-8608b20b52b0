import React from 'react';
import { nanoid } from 'nanoid';

import Wysiwyg from '../wysiwyg';
import { RowType } from './interface';
import { TooltipContainer } from './styles';

// 默认行数据
const defaultRows: RowType[] = [
  {
    id: nanoid(),
    elements: [
      {
        id: nanoid(),
        type: 'Text',
        content: 'Track your favorites',
      },
    ],
  },
  {
    id: nanoid(),
    elements: [
      {
        id: nanoid(),
        type: 'Text',
        content: 'Anything you favorite will be stored here for safekeeping.',
      },
    ],
  },
  {
    id: nanoid(),
    elements: [
      {
        id: nanoid(),
        type: 'Text',
        content: '⊘ Hide these tips',
      },
      {
        id: nanoid(),
        type: 'Button',
        content: '按钮',
      },
    ],
  },
];

const TooltipEditor: React.FC = () => {
  return (
    <TooltipContainer>
      {defaultRows.map((row) => (
        <div key={row.id}>
          {row.elements.map((element) => (
            <Wysiwyg key={element.id} {...element} />
          ))}
        </div>
      ))}
    </TooltipContainer>
  );
};

export default TooltipEditor;
