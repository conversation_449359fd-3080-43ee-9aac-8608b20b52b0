import React, { useCallback, useState } from 'react';

import { Button, Flex } from '@/components';
import { ModeEnum } from '@/editor/utils/enum';
import {
  AimOutlined,
  CaretDownFilled,
  CaretUpFilled,
  CloseCircleFilled,
  CopyFilled,
  DeleteFilled,
  VerticalLeftOutlined,
  VerticalRightOutlined,
} from '@ant-design/icons';

import useEditorStore from '../../../stores';
import TooltipContent from './components/tooltip-content';
import { Container, Header, HeaderRight, HeaderTitle, Operation, OperationSection } from './styles';

const StepPanel = () => {
  const { mode, setMode, stepType, setStepType, step, updateStep, resetStep, addStep, removeStep } = useEditorStore();
  const [show, setShow] = useState<boolean>(true);
  const [left, setLeft] = useState<boolean>(true);

  const handleShow = () => {
    setShow(!show);
  };

  const handleChangePosition = () => {
    setLeft(!left);
  };

  const handleInitEvent = () => {
    setMode(ModeEnum.TARGET);
  };

  const handleDelete = useCallback(() => {
    if (step?.stepId) {
      removeStep(step.stepId);
      resetStep();
      setStepType(undefined);
    }
  }, [step]);

  const renderHeaderButton = useCallback(() => {
    return (
      <OperationSection>
        <Button
          shape='circle'
          type='text'
          onClick={handleInitEvent}
          icon={<AimOutlined style={{ fontSize: '12px', color: '#fff' }} />}
        />
        <Button shape='circle' type='text' icon={<CopyFilled style={{ fontSize: '12px', color: '#fff' }} />} />
        <Button
          shape='circle'
          type='text'
          icon={<DeleteFilled style={{ fontSize: '12px', color: '#fff' }} />}
          onClick={handleDelete}
          disabled={!step?.stepId}
        />
      </OperationSection>
    );
  }, []);

  if (!stepType) {
    return null;
  }

  return (
    <Container $show={show} $left={left}>
      <Operation $left={left} onClick={handleShow}>
        {show ? '隐藏' : '显示'}
        {show ? (
          <CaretDownFilled style={{ fontSize: '12px', color: '#fff', marginLeft: '4px' }} />
        ) : (
          <CaretUpFilled style={{ fontSize: '12px', color: '#fff', marginLeft: '4px' }} />
        )}
      </Operation>
      <Header>
        <HeaderTitle>编辑当前步骤</HeaderTitle>
        <HeaderRight>
          <Flex gap='middle'>
            {left ? (
              <VerticalRightOutlined style={{ fontSize: '18px', color: '#fff' }} onClick={handleChangePosition} />
            ) : (
              <VerticalLeftOutlined style={{ fontSize: '18px', color: '#fff' }} onClick={handleChangePosition} />
            )}
            <CloseCircleFilled style={{ fontSize: '18px', color: '#fff' }} onClick={handleShow} />
          </Flex>
        </HeaderRight>
      </Header>
      {renderHeaderButton()}
      {mode === ModeEnum.EDIT && step?.stepId && (
        <TooltipContent step={step} updateStep={updateStep} addStep={addStep} />
      )}
    </Container>
  );
};

export default StepPanel;
