import styled from 'styled-components';

import { Form, Radio } from '@/components';

const AttrsForm = styled(Form)`
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
  padding: 20px 24px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 16px;
    height: 12px;
  }

  &::-webkit-scrollbar-button {
    display: none;
    width: 0px;
    height: 0px;
  }

  &::-webkit-scrollbar-corner {
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    border-width: 4px;
    border-style: solid;
    border-color: transparent;
    border-radius: 10px;
    border-image: initial;
    background-clip: padding-box;
  }

  &::-webkit-scrollbar-track {
    border-width: 4px;
    border-style: solid;
    border-color: transparent;
    border-radius: 10px;
    border-image: initial;
    background: padding-box rgba(0, 0, 0, 0.2);
  }

  // 覆盖样式组件
  & :global {
    .flowez-radio-group {
      display: flex;
      gap: 20px;
    }
  }
`;

const RadioGroup = styled(Radio.Group)`
  display: flex;
  gap: 20px;

  & .flowez-radio-button-wrapper {
    border-radius: 4px;

    &:hover::before,
    &::before {
      background: transparent;
    }
  }
`;

const Label = styled.span`
  & .anticon {
    margin-right: 4px;
  }
`;

const LocationContainer = styled.div`
  margin-top: 10px;
  padding: 0 6px;
`;

const LocationTB = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-between;
  padding: 0px 36px;
`;

const LocationLRContainer = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-between;
`;

const LocationLRContent = styled.div`
  align-items: center;
  background: #424b5e;
  border-radius: 4px;
  display: flex;
  flex-grow: 1;
  height: 80px;
  justify-content: center;
  margin: 10px;
  //padding: 24px 12px;
  position: relative;
  width: 200px;
`;

const LocationLR = styled.div`
  align-items: center;
  flex-direction: column;
  display: flex;
  height: 60px;
  justify-content: space-between;
  padding: 12px 0px;
`;

export {
  AttrsForm,
  RadioGroup,
  Label,
  LocationContainer,
  LocationTB,
  LocationLRContainer,
  LocationLRContent,
  LocationLR,
};
