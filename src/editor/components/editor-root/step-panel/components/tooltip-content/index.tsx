import React, { useEffect } from 'react';

import ColorIcon from '@/assets/icon/color.svg';
import LocationIcon from '@/assets/icon/location.svg';
import PersonIcon from '@/assets/icon/person.svg';
import RadiusIcon from '@/assets/icon/radius.svg';
import { ColorPicker, ConfigProvider, Form, Input, InputNumber, Radio } from '@/components';
import Icon, { CloseCircleFilled, EyeFilled } from '@ant-design/icons';

import {
  AttrsForm,
  Label,
  LocationContainer,
  LocationLR,
  LocationLRContainer,
  LocationLRContent,
  LocationTB,
  RadioGroup,
} from './styles';

const TooltipContent = (props: any) => {
  const { step, updateStep } = props;
  const [form] = Form.useForm();
  const locationType = Form.useWatch('locationType', form);

  useEffect(() => {
    form.setFieldsValue(step);
  }, [step]);

  const handleValuesChange = (_: any, values: any) => {
    const data = {
      ...step,
      ...values,
      color: typeof values.color === 'string' ? values.color : values.color?.toHexString(),
    };
    updateStep(data);
  };

  return (
    <ConfigProvider
      theme={{
        components: {
          Form: {
            labelColor: '#fff',
          },
          Radio: {
            buttonPaddingInline: 10,
            buttonBg: '#424B5E',
            colorBorder: '#424B5E',
            buttonColor: '#FFFFFF',
          },
        },
      }}
    >
      <AttrsForm layout='vertical' form={form} onValuesChange={handleValuesChange}>
        <Form.Item
          label={
            <Label>
              <Icon component={ColorIcon} />
              背景色
            </Label>
          }
          name='color'
        >
          <ColorPicker showText format='hex' />
        </Form.Item>
        <ConfigProvider
          theme={{
            components: {
              InputNumber: {
                colorBgContainer: '#424B5E',
                colorBorder: '#424B5E',
                colorText: '#fff',
                activeBorderColor: '#424B5E',
                hoverBorderColor: '#424B5E',
              },
            },
          }}
        >
          <Form.Item
            label={
              <Label>
                <Icon component={RadiusIcon} />
                圆角
              </Label>
            }
            name='radius'
          >
            <InputNumber step={1} min={0} />
          </Form.Item>
        </ConfigProvider>
        <Form.Item
          label={
            <Label>
              <CloseCircleFilled />
              是否允许关闭当前步骤
            </Label>
          }
          name='closeStep'
        >
          <RadioGroup buttonStyle='solid'>
            <Radio.Button value='not'>不允许</Radio.Button>
            <Radio.Button value='hidden'>允许隐藏</Radio.Button>
            <Radio.Button value='close'>允许关闭</Radio.Button>
          </RadioGroup>
        </Form.Item>
        <Form.Item
          label={
            <Label>
              <Icon component={LocationIcon} />
              步骤位置
            </Label>
          }
          name='locationType'
        >
          <RadioGroup buttonStyle='solid'>
            <Radio.Button value='auto'>自动定位</Radio.Button>
            <Radio.Button value='custom'>手动定位</Radio.Button>
          </RadioGroup>
        </Form.Item>
        {locationType !== 'custom' && (
          <Form.Item name='locationCustom'>
            <LocationContainer>
              <ConfigProvider
                theme={{
                  components: {
                    Radio: {
                      colorBgContainer: '#424B5E',
                      colorBorder: '#424B5E',
                      radioSize: 10,
                      radioColor: '#406CF6',
                      radioBgColor: '#424B5E',
                      wrapperMarginInlineEnd: 0,
                    },
                  },
                }}
              >
                <Radio.Group size='small'>
                  <LocationTB>
                    <Radio value='topLeft' />
                    <Radio value='top' />
                    <Radio value='topRight' />
                  </LocationTB>
                  <LocationLRContainer>
                    <LocationLR>
                      <Radio value='leftTop' />
                      <Radio value='left' />
                      <Radio value='leftBottom' />
                    </LocationLR>
                    <LocationLRContent />
                    <LocationLR>
                      <Radio value='rightTop' />
                      <Radio value='right' />
                      <Radio value='rightBottom' />
                    </LocationLR>
                  </LocationLRContainer>
                  <LocationTB>
                    <Radio value='bottomLeft' />
                    <Radio value='bottom' />
                    <Radio value='bottomRight' />
                  </LocationTB>
                </Radio.Group>
              </ConfigProvider>
            </LocationContainer>
          </Form.Item>
        )}
        <Form.Item
          hidden={locationType === 'auto'}
          style={{
            padding: '12px',
            backgroundColor: 'rgba(66, 75, 92, 0.5)',
            borderRadius: '4px',
          }}
        >
          <Form.Item label='CSS精准定位' name='customLocation'>
            <Input.TextArea />
          </Form.Item>
          <Form.Item label='文本精准定位' name='textLocation'>
            <Input />
          </Form.Item>
        </Form.Item>
        <Form.Item
          label={
            <Label>
              <EyeFilled />
              哪个页面显示
            </Label>
          }
          name='pageRange'
        >
          <RadioGroup buttonStyle='solid'>
            <Radio.Button value='all'>所有页面</Radio.Button>
            <Radio.Button value='part'>部分页面</Radio.Button>
          </RadioGroup>
        </Form.Item>
        <Form.Item
          label={
            <Label>
              <Icon component={PersonIcon} />
              谁可以看
            </Label>
          }
          name='userRange'
        >
          <RadioGroup buttonStyle='solid'>
            <Radio.Button value='all'>所有用户</Radio.Button>
            <Radio.Button value='part'>部分用户</Radio.Button>
          </RadioGroup>
        </Form.Item>
      </AttrsForm>
    </ConfigProvider>
  );
};

export default TooltipContent;
