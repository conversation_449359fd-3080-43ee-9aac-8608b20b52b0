import styled from 'styled-components';

const Container = styled.div<{ $show: boolean; $left?: boolean }>`
  width: 316px;
  height: calc(100% - 60px);
  background-color: #252a34;
  display: flex;
  position: fixed;
  flex-direction: column;
  top: 0;
  left: ${(props) => (props.$left ? 'unset' : 0)};
  right: ${(props) => (props.$left ? 0 : 'unset')};
  transition: transform 200ms ease-in-out 0s;
  transform: ${(props) => (props.$show ? 'translateX(0)' : props.$left ? 'translateX(100%)' : 'translateX(-100%)')};
  box-shadow: rgba(0, 0, 0, 0.25) 0px -4px 16px;
  z-index: 100;
`;

const Operation = styled.div<{ $left?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 54px;
  height: 20px;
  color: #ffffff;
  background-color: #6e7177;
  font-size: 10px;
  position: absolute;
  bottom: 100%;
  cursor: pointer;
  border-top-right-radius: ${(props) => (props.$left ? 0 : '4px')};
  border-top-left-radius: ${(props) => (props.$left ? '4px' : 0)};
  transition:
    background-color 200ms ease-in-out 0s,
    color 200ms ease-in-out 0s;
  top: 17px;
  left: ${(props) => (props.$left ? 'unset' : '298px')};
  right: ${(props) => (props.$left ? '299px' : 'unset')};
  transform: ${(props) => (props.$left ? 'rotate(-90deg)' : 'rotate(90deg)')};
`;

const Header = styled.header`
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  height: 54px;
  justify-content: space-between;
  align-items: center;
  padding: 8px 24px;
  background-color: #424b5e;
`;

const HeaderTitle = styled.h2`
  color: #fff;
  font-size: 14px;
  font-weight: 600;
`;

const HeaderRight = styled.div`
  display: flex;
`;

const OperationSection = styled.section`
  height: 50px;
  display: flex;
  flex-shrink: 0;
  justify-content: space-around;
  align-items: center;
  background-color: #424b5e;
`;

const ChooseSection = styled.section`
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 12px;
`;

const Figure = styled.figure`
  background: #627293;
  border-radius: 6px;
  border-top: 2px solid transparent;
  box-shadow: rgba(36, 42, 53, 0.4) 0px 4px 6px;
  color: #e7ecf3;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  -webkit-box-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  align-items: center;
  height: 128px;
  width: 130px;
  margin: 0px 0px 24px;

  img {
    margin: auto;
    width: auto;
    max-width: 100px;
    max-height: 75px;
  }
`;

const Figcaption = styled.figcaption`
  background: #394455;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  width: 100%;
  height: 32px;
  text-align: center;
  line-height: 32px;
`;

export { Container, Operation, Header, HeaderTitle, HeaderRight, OperationSection, ChooseSection, Figure, Figcaption };
