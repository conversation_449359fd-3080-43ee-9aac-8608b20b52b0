import React from 'react';

import { ConfigProvider } from '@/components';
import PortalRoot from '../portal-root';
import { getEditorRoot, getPortalRoot } from '../../utils/document';
import StepPanel from './step-panel';
import ToolBar from './tool-bar';
import StepEditor from './step-editor';

const EditorRoot: React.FC = () => {

  return (
    <>
      <ConfigProvider
        getPopupContainer={(triggerNode) => {
          const $editorRoot = getEditorRoot() as HTMLElement;
          if ($editorRoot) {
            return $editorRoot;
          }
          return triggerNode?.parentElement || document.body;
        }}
      >
        <div id='editor-root'>
          <ToolBar />
          <StepEditor />
          <StepPanel />
        </div>
      </ConfigProvider>
      <ConfigProvider
        getPopupContainer={(triggerNode) => {
          const $portalRoot = getPortalRoot() as HTMLElement;
          if ($portalRoot) {
            return $portalRoot;
          }
          return triggerNode?.parentElement || document.body;
        }}
        theme={{
          components: {
            Popover: {
              colorBgElevated: '#fff',
            },
          },
        }}
      >
        <div id='portal-root'>
          <PortalRoot />
        </div>
      </ConfigProvider>
    </>
  );
};

export default EditorRoot;
