import React, { useState } from 'react';
import Icon, { CaretDownFilled, CaretUpFilled, PlusOutlined } from '@ant-design/icons';
import { DndContext, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import type { DragEndEvent } from '@dnd-kit/core';
import { restrictToHorizontalAxis } from '@dnd-kit/modifiers';
import { arrayMove, horizontalListSortingStrategy, SortableContext } from '@dnd-kit/sortable';

import CheckIcon from '@/assets/icon/check.svg';
import ExitIcon from '@/assets/icon/exit.svg';
import EyeIcon from '@/assets/icon/eye.svg';
import SettingIcon from '@/assets/icon/setting.svg';
import Logo from '@/assets/svg/logo.svg';
import { Button, Flex, Menu, Popover } from '@/components';
import type { MenuProps } from '@/components';

import useEditorStore from '../../../stores';
import { StepTypeEnum } from '../../../utils/enum';
import StepItem from './StepItem';
import { Center, Container, Content, DraggableContainer, Input, Left, Operation, Right } from './style';

type MenuItem = Required<MenuProps>['items'][number];

// 抽取类型定义
interface ToolBarProps {
  show?: boolean;
}

// 抽取步骤类型菜单项
const STEP_TYPE_ITEMS: MenuItem[] = [
  { key: StepTypeEnum.TOOLTIP, label: '气泡窗' },
  { key: StepTypeEnum.MODAL, label: '弹窗' },
];

// 抽取拖拽相关配置
const DND_SENSOR_CONFIG = {
  activationConstraint: {
    distance: 2,
  },
};

const ToolBar: React.FC<ToolBarProps> = ({ show = true }) => {
  const { guideName, setGuideName, setStepType, step, resetStep, steps, setSteps } = useEditorStore();
  const [toolBarShow, setToolBarShow] = useState(show);

  const sensors = useSensors(useSensor(PointerSensor, DND_SENSOR_CONFIG));

  const handleToolBarShow = () => setToolBarShow((prev) => !prev);

  const handleSelectStep = (key: StepTypeEnum) => {
    setStepType(key);
    resetStep();
  };

  // 抽取步骤类型渲染逻辑
  const renderStepType = () => (
    <Menu
      mode='inline'
      items={STEP_TYPE_ITEMS}
      selectedKeys={[]}
      onClick={({ key }) => handleSelectStep(key as StepTypeEnum)}
    />
  );

  // 优化拖拽结束处理函数
  const handleDragEnd = (dragItem: DragEndEvent) => {
    const { active, over } = dragItem;
    if (!active || !over || active.id === over.id) return;

    const activeIndex = steps.findIndex((item) => item.stepId === active.id);
    const overIndex = steps.findIndex((item) => item.stepId === over.id);

    if (activeIndex !== -1 && overIndex !== -1) {
      const newSteps = arrayMove([...steps], activeIndex, overIndex);
      setSteps(newSteps);
    }
  };

  // 抽取步骤列表渲染逻辑
  const renderStepList = () => (
    <DndContext sensors={sensors} modifiers={[restrictToHorizontalAxis]} onDragEnd={handleDragEnd}>
      <SortableContext
        items={steps.filter((s) => s.stepId).map((s) => s.stepId!)}
        strategy={horizontalListSortingStrategy}
      >
        <DraggableContainer>
          {steps.map((item) => (
            <StepItem key={item.stepId} item={item} selected={item.stepId === step?.stepId} />
          ))}
        </DraggableContainer>
      </SortableContext>
    </DndContext>
  );

  // 抽取右侧按钮组渲染逻辑
  // const renderActionButtons = () => (
  //   <Flex align='center' justify='center' gap={8}>
  //     {[SettingIcon, EyeIcon, CheckIcon, ExitIcon].map((IconComponent, index) => (
  //       <Button
  //         key={index}
  //         icon={<Icon component={IconComponent} style={{ fontSize: '22px' }} />}
  //       />
  //     ))}
  //   </Flex>
  // );

  const handleSave = () => {
    console.log('=====steps', steps);
  };

  return (
    <Container $show={toolBarShow}>
      <Operation onClick={handleToolBarShow}>
        {toolBarShow ? '隐藏' : '显示'}
        {toolBarShow ? (
          <CaretDownFilled style={{ fontSize: '12px', color: '#fff', marginLeft: '4px' }} />
        ) : (
          <CaretUpFilled style={{ fontSize: '12px', color: '#fff', marginLeft: '4px' }} />
        )}
      </Operation>
      <Content>
        <Left>
          <Icon component={Logo} style={{ fontSize: '30px' }} />
          <Input placeholder='请输入引导名称' value={guideName} onChange={(e) => setGuideName(e.target.value)} />
        </Left>
        <Center>
          {renderStepList()}
          <Popover content={renderStepType} trigger='click'>
            <Button type='primary' icon={<PlusOutlined />}>
              添加步骤
            </Button>
          </Popover>
        </Center>
        <Right>
          <Flex align='center' justify='center' gap={8}>
            <Button icon={<Icon component={SettingIcon} style={{ fontSize: '22px' }} />}></Button>
            <Button icon={<Icon component={EyeIcon} style={{ fontSize: '22px' }} />} />
            <Button icon={<Icon component={CheckIcon} style={{ fontSize: '22px' }} />} onClick={handleSave} />
            <Button icon={<Icon component={ExitIcon} style={{ fontSize: '22px' }} />} />
          </Flex>
        </Right>
      </Content>
    </Container>
  );
};

export default React.memo(ToolBar);
