import React, { FC } from 'react';
import Icon from '@ant-design/icons';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import ModalIcon from '@/assets/icon/modal.svg';
import TooltipIcon from '@/assets/icon/tooltip.svg';
import { Step } from '@/editor/interface';

import useEditorStore from '../../../stores';
import { StepTypeEnum } from '../../../utils/enum';
import { DraggableItem as DraggableItemStyle } from './style';

type StepItemProps = {
  item: Step;
  selected: boolean;
};

const StepItem: FC<StepItemProps> = ({ item, selected }) => {
  const { setStep, setStepType } = useEditorStore();
  const { setNodeRef, attributes, listeners, transform, transition } = useSortable({
    id: item.stepId!,
    transition: {
      duration: 500,
      easing: 'cubic-bezier(0.25, 1, 0.5, 1)',
    },
  });
  const styles = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const handleSelect = () => {
    setStep(item);
    setStepType(item.stepType);
  };

  return (
    <DraggableItemStyle
      $selected={selected}
      ref={setNodeRef}
      {...attributes}
      {...listeners}
      style={styles}
      onClick={() => handleSelect()}
    >
      <Icon component={item.stepType === StepTypeEnum.MODAL ? ModalIcon : TooltipIcon} style={{ fontSize: '22px' }} />
    </DraggableItemStyle>
  );
};

export default StepItem;
