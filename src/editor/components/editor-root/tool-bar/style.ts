import styled from 'styled-components';

import { Input as BaseInput } from '@/components';

const Container = styled.div<{ $show: boolean }>`
  background-color: #252a34;
  bottom: 0;
  left: 0;
  position: fixed;
  width: 100%;
  height: 60px;
  transform: ${(props) => (props.$show ? 'translateY(0)' : 'translateY(100%)')};
  transition: transform 200ms ease-in-out 0s;
  box-shadow: rgba(0, 0, 0, 0.25) 0px -4px 16px;
  z-index: 100;
`;

const Operation = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 54px;
  height: 20px;
  color: #ffffff;
  background-color: #6e7177;
  font-size: 10px;
  position: absolute;
  left: 0;
  bottom: 100%;
  cursor: pointer;
  border-top-right-radius: 4px;
  transition:
    background-color 200ms ease-in-out 0s,
    color 200ms ease-in-out 0s;
`;

const Content = styled.div`
  display: flex;
  align-items: center;
  height: inherit;
  justify-content: space-between;
  padding: 0 20px;
`;

const Left = styled.div`
  display: flex;
  gap: 8px;
`;

const Center = styled.div`
  width: 100%;
  padding: 0 10px;
  display: flex;
  gap: 8px;
`;

const DraggableContainer = styled.div`
  display: flex;
  gap: 8px;
`;

const DraggableItem = styled.div<{ $selected: boolean }>`
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${(props) => (props.$selected ? '#406cf6' : '#424B5E')};
  border-radius: 4px;
  cursor: pointer;
  color: #fff;
`;

const Right = styled.div``;

const Input = styled(BaseInput)`
  width: 120px;
`;

export { Container, Operation, Content, Input, Left, Center, DraggableContainer, DraggableItem, Right };
