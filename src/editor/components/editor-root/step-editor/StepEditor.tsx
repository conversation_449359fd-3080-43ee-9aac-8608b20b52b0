import React from 'react';
import { EditorMain } from './styles';
import Frame from '../../Frame';

// const EditorMain = styled(root.main)`
//   left: 0;
//   position: fixed;
//   top: 0;
//   z-index: -1;
// `;

const StepEditor: React.FC = () => {
  return (
    <EditorMain id='editor-main'>
      <Frame
        style={{
          colorScheme: 'none',
          border: 'none',
          height: 'calc(100% - 64px)',
          left: '0px',
          margin: '0px',
          outline: 'none',
          padding: '0px',
          position: 'fixed',
          top: '0px',
          width: '100%',
        }}
      >
        <div>ksghdaflkhskldfhaslkhd</div>
      </Frame>
    </EditorMain>
  );
};

export default StepEditor;
