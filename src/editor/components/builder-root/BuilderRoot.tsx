import React, { useEffect, useCallback } from 'react';

import useGlobalStore from '@/stores/global';

import useEditorStore from '../../stores';
import ThemeProvider from '../theme-provider';
import ErrorBoundary from '../error-boundary';
import EditorRoot from '../editor-root';
import TargetElement from '../target-element';
import { getBuilderRoot } from '../../utils/document';
import { ModeEnum } from '../../utils/enum';

interface Props {
  options: InitSDKOptions;
}

const BuilderRoot = (props: Props) => {
  const { options } = props;
  const { setInitSDKOptions } = useGlobalStore();
  const { mode } = useEditorStore();
  const $builderRoot = getBuilderRoot();

  useEffect(() => {
    setInitSDKOptions(options);
  }, [options, setInitSDKOptions]);

  const renderMode = useCallback(() => {
    switch (mode) {
      case ModeEnum.PREVIEW:
        return null;
      case ModeEnum.TARGET:
        return <TargetElement />;
      case ModeEnum.EDIT:
      default:
        return <EditorRoot />;
    }
  }, [mode]);

  return (
    <ThemeProvider target={$builderRoot}>
      <ErrorBoundary fallback={<div>BuilderRoot Error</div>}>{renderMode()}</ErrorBoundary>
    </ThemeProvider>
  );
};

export default BuilderRoot;
