import { styled, css } from 'styled-components';
import { Position } from './interface';

const TooltipContainer = styled.div`
  max-width: 600px;
  min-width: 200px;
  color: #000;
  padding: 12px;
`;

const AddRowContainer = styled.div<{
  $isHovering: boolean;
  $top?: boolean;
  $isHoveringOverAddButton?: boolean;
  $isElement?: boolean;
}>`
  ${({ $isElement, $top, $isHoveringOverAddButton }) =>
    $isElement
      ? css`
          height: 100%;
          min-height: 100%;
          width: ${() => ($isHoveringOverAddButton ? '32px' : '0px')};
          min-width: 0px;
          right: 3px;
          top: 0px;
        `
      : css`
          top: ${() => ($top ? '-4.5px' : undefined)};
          bottom: ${() => (!$top ? '16px' : undefined)};
          left: 0px;
          right: 0px;
          width: 100%;
        `}
  box-sizing: border-box;
  position: absolute;
  opacity: ${({ $isHovering }) => ($isHovering ? 1 : 0)};
  transition:
    opacity 0.15s ease-out,
    height 0.15s ease-out,
    transform;
  backface-visibility: hidden;
  transform: translateZ(0px);
  z-index: 2;
`;

const AddButton = styled.div<{ $isHoveringOverAddButton: boolean; $isElement?: boolean }>`
  ${({ $isElement, $isHoveringOverAddButton }) =>
    $isElement
      ? css`
          top: 50%;
          right: 0px;
          transform: translateY(-50%) translateX(50%) scale(${() => ($isHoveringOverAddButton ? 1.25 : 1)});
          transform-origin: right center;
        `
      : css`
          top: 10px;
          left: 50%;
          transform: translateX(-50%) translateY(-50%) scale(${() => ($isHoveringOverAddButton ? 2 : 1)});
          transform-origin: center center 0px;
        `}
  position: absolute;
  cursor: pointer;
  transition: transform 0.15s ease-out;
  z-index: 3;
`;

const AddBorder = styled.section<{ $isHovering: boolean; $isHoveringOverAddButton: boolean; $isElement?: boolean }>`
  ${({ $isElement, $isHoveringOverAddButton }) =>
    $isElement
      ? css`
          height: 100%;
          top: 0px;
          width: ${() => ($isHoveringOverAddButton ? '32px' : '1px')};
          transform-origin: right center;
          transform: translateZ(0px) translateX(-1px);
        `
      : css`
          height: ${() => ($isHoveringOverAddButton ? '40px' : '2px')};
          top: 10px;
          width: 100%;
          transform-origin: center 25% 0px;
          transform: translateY(-50%) translateZ(0px);
        `}
  box-sizing: border-box;
  border: ${({ $isHoveringOverAddButton }) =>
    $isHoveringOverAddButton ? '2px dotted rgb(0, 184, 80)' : '1px solid rgb(0, 184, 80)'};
  position: absolute;
  padding: 0px;
  margin: 0px;
  pointer-events: none;
  background: rgb(212, 254, 230);
  opacity: ${({ $isHovering }) => ($isHovering ? 0.5 : 0)};
  transition:
    height 0.15s ease-out,
    transform,
    opacity 0.3s ease-out 0.1s;
  backface-visibility: hidden;
`;

const RowContainer = styled.div<{ $isHover: boolean; $isDragging: boolean; $insertPosition?: Position }>`
  min-height: 32px;
  line-height: 32px;
  position: relative;
  background-color: ${({ $isHover, $isDragging }) =>
    $isHover && !$isDragging ? 'rgb(0, 178, 229, 0.14)' : $isDragging ? 'rgba(230, 230, 230)' : 'transparent'};
  padding: 0 12px;
  z-index: 3;
`;

const RowDragIcon = styled.section<{ $isHover: boolean }>`
  cursor: grab;
  opacity: ${({ $isHover }) => ($isHover ? 1 : 0)};
  position: absolute;
  background: rgb(0, 178, 229);
  height: 100%;
  width: 20px;
  left: -20px;
  top: 0px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px 0px 0px 4px;
`;

const RowContent = styled.div<{ $isDragging: boolean; $insertPosition?: Position; $isClone: boolean; $isRow: boolean }>`
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  ${({ $isDragging, $isClone, $insertPosition, $isRow }) =>
    $isRow &&
    !$isDragging &&
    !$isClone &&
    $insertPosition &&
    css`
      &:after {
        position: absolute;
        background-color: #4c9ffe;
        content: '';
      }
    `}
  ${({ $insertPosition, $isRow }) =>
    $isRow &&
    $insertPosition &&
    css`
      &:after {
        top: ${$insertPosition === Position.Before ? '0px' : 'unset'};
        right: 0px;
        bottom: ${$insertPosition === Position.After ? '0px' : 'unset'};
        left: 0px;
        height: 2px;
      }
    `}
`;

const ElementContainer = styled.div<{ $isDragging: boolean; $isClone: boolean; $insertPosition?: Position }>`
  flex: 1 1 0%;
  background-color: ${({ $isDragging }) => ($isDragging ? 'rgba(230, 230, 230)' : 'transparent')};

  ${({ $isDragging, $isClone, $insertPosition }) =>
    !$isDragging &&
    !$isClone &&
    $insertPosition &&
    css`
      &:after {
        position: absolute;
        background-color: #4c9ffe;
        content: '';
      }
    `}
  ${({ $insertPosition }) =>
    $insertPosition &&
    css`
      &:after {
        top: 0;
        right: ${$insertPosition === Position.After ? '-4px' : 'unset'};
        bottom: 0;
        left: ${$insertPosition === Position.Before ? '-4px' : 'unset'};
        width: 2px;
      }
    `}
`;

const ElementContent = styled.div<{ $isHover: boolean }>`
  outline: ${({ $isHover }) => ($isHover ? 'rgb(0, 178, 229) dotted 2px' : 'transparent dotted 2px')};
  display: inline-block;
  position: relative;
  margin: 0px;
  padding: 0px;
  width: 100%;
  height: 100%;
  transition:
    background-color 0.15s ease-out,
    box-shadow 0.15s ease-out,
    outline-color 0.15s ease-out;
  background-color: ${({ $isHover }) => ($isHover ? 'rgb(0, 178, 229, 0.14)' : undefined)};
  cursor: grab;
`;

export {
  TooltipContainer,
  AddRowContainer,
  AddButton,
  AddBorder,
  RowContainer,
  RowDragIcon,
  RowContent,
  ElementContainer,
  ElementContent,
};
