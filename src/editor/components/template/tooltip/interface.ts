export enum Position {
  Before = -1,
  After = 1,
}

export interface ElementType {
  id: string;
  type: 'Text' | 'Button' | 'Image' | 'Link';
  content: string;
  style?: React.CSSProperties;
}

export interface RowType {
  id: string;
  elements: ElementType[];
  style?: React.CSSProperties;
}

export interface TooltipTemplateProps {
  // 初始行数据
  initialRows?: RowType[];

  // 工具提示的样式
  containerStyle?: React.CSSProperties;

  // 是否显示添加行按钮
  showAddRowButton?: boolean;

  // 是否允许拖拽
  enableDragging?: boolean;

  // 是否显示悬停效果
  enableHoverEffect?: boolean;

  // 元素类型列表（可用的元素类型）
  availableElementTypes?: ('Text' | 'Button' | 'Image' | 'Link')[];

  // 行变更时的回调
  onRowsChange?: (newRows: RowType[]) => void;

  // 其他可选回调
  onElementAdd?: (element: ElementType, rowId: string) => void;
  onElementRemove?: (elementId: string, rowId: string) => void;
  onRowAdd?: (row: RowType) => void;
  onRowRemove?: (rowId: string) => void;
}
