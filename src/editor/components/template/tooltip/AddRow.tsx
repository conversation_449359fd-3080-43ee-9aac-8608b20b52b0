import React, { useState } from 'react';
import { PlusCircleFilled } from '@ant-design/icons';
import { nanoid } from 'nanoid';

import { AddRowContainer, AddButton, AddBorder } from './style';
import { RowType } from './interface';

interface AddRowProps {
  isHovering: boolean;
  top?: boolean;
  isElement?: boolean;
  onAddRow?: (row: RowType) => void;
}

const AddRow: React.FC<AddRowProps> = ({ isHovering, top = false, isElement = false, onAddRow }) => {
  const [isHoveringOverAddButton, setIsHoveringOverAddButton] = useState(false);

  const handleClick = () => {
    if (onAddRow) {
      // 创建新行
      const newRow: RowType = {
        id: nanoid(),
        elements: [
          {
            id: nanoid(),
            type: 'Text',
            content: '新文本',
          },
        ],
      };
      onAddRow(newRow);
    }
  };

  return (
    <AddRowContainer
      $isHovering={isHovering}
      $top={top}
      $isHoveringOverAddButton={isHoveringOverAddButton}
      $isElement={isElement}
    >
      <AddButton
        onClick={handleClick}
        onMouseEnter={() => setIsHoveringOverAddButton(true)}
        onMouseLeave={() => setIsHoveringOverAddButton(false)}
        $isHoveringOverAddButton={isHoveringOverAddButton}
        $isElement={isElement}
      >
        <PlusCircleFilled style={{ color: 'rgb(0, 209, 91)' }} />
      </AddButton>
      <AddBorder $isHovering={isHovering} $isHoveringOverAddButton={isHoveringOverAddButton} $isElement={isElement} />
    </AddRowContainer>
  );
};

export default AddRow;
