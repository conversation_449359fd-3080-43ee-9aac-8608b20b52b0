import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ModeEnum, StepTypeEnum } from '../utils/enum';
import { Flow, Step } from '../interface';

interface State {
  guideName: string;
  mode: ModeEnum;
  stepType: StepTypeEnum | undefined;
  step: Step | undefined;
  steps: Step[];
  flow: Flow | undefined;
}

interface Actions {
  setGuideName: (value: string) => void;
  // 操作类型
  setMode: (value: ModeEnum) => void;
  setStepType: (value: StepTypeEnum | undefined) => void;

  // 当前步骤
  setStep: (value: Step) => void;
  resetStep: () => void;
  updateStep: (value: Partial<Step>) => void;

  // 步骤列表
  setSteps: (value: Step[]) => void;
  addStep: (value: Step) => void;
  removeStep: (stepId: string) => void;

  // 引导
  setFlow: (value: Flow) => void;

  getState: () => State;
}

const initialState: State = {
  guideName: '',
  mode: ModeEnum.EDIT,
  stepType: undefined,
  step: undefined,
  steps: [],
  flow: undefined,
};

const useEditorStore = create<State & Actions>()(
  devtools((set, get) => ({
    ...initialState,

    setGuideName: (value: string) => set({ guideName: value }),

    setMode: (value: ModeEnum) => set({ mode: value }),
    setStepType: (value) => set({ stepType: value }),

    setStep: (value: Step) => set({ step: value }),
    resetStep: () => set({ step: undefined }),
    updateStep: (value: Partial<Step>) =>
      set((state) => ({
        step: state.step ? { ...state.step, ...value } : undefined,
        steps: state.steps.map((step) => (step.stepId === value.stepId ? { ...step, ...value } : step)),
      })),

    setSteps: (value: Step[]) => set({ steps: value }),
    addStep: (value: Step) => set((state) => ({ steps: [...state.steps, value] })),
    removeStep: (stepId: string) =>
      set((state) => ({
        steps: state.steps.filter((step) => step.stepId !== stepId),
      })),

    setFlow: (value: Flow) => set({ flow: value }),

    getState: () => get(),
  }))
);

export default useEditorStore;
