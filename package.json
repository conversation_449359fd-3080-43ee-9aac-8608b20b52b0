{"name": "flowez-sdk", "private": true, "version": "1.0.0", "scripts": {"build": "cross-env NODE_ENV=production rspack build", "dev": "cross-env NODE_ENV=development rspack serve", "format": "prettier --write .", "lint": "eslint ."}, "dependencies": {"@ant-design/colors": "^8.0.0", "@ant-design/cssinjs": "^1.23.0", "@ant-design/cssinjs-utils": "^1.1.3", "@ant-design/icons": "^6.0.0", "@ctrl/tinycolor": "^4.1.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@flow-ez/unique-selector": "^0.0.1", "@rc-component/color-picker": "~3.0.1", "@rc-component/mutate-observer": "^2.0.0", "@rc-component/tour": "~2.1.5", "@rc-component/trigger": "^3.2.0", "antd-style": "^3.7.1", "classnames": "^2.5.1", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.13", "lodash": "^4.17.21", "nanoid": "^5.1.5", "rc-cascader": "~3.34.0", "rc-checkbox": "~3.5.0", "rc-collapse": "~4.0.0", "rc-dialog": "~10.0.0", "rc-drawer": "~8.0.0", "rc-field-form": "~2.7.0", "rc-image": "~7.12.0", "rc-input": "~1.8.0", "rc-input-number": "~9.5.0", "rc-mentions": "~2.20.0", "rc-menu": "^9.16.1", "rc-motion": "^2.9.5", "rc-notification": "~5.6.4", "rc-picker": "~4.11.3", "rc-progress": "~4.0.0", "rc-rate": "~2.13.1", "rc-resize-observer": "^1.4.3", "rc-segmented": "~2.7.0", "rc-select": "~14.16.6", "rc-slider": "~11.1.8", "rc-steps": "~6.0.1", "rc-switch": "~4.1.0", "rc-tabs": "~15.6.1", "rc-textarea": "~1.10.0", "rc-tooltip": "~6.4.0", "rc-tree": "~5.13.1", "rc-tree-select": "~5.27.0", "rc-upload": "~4.9.0", "rc-util": "^5.44.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-frame-component": "^5.2.7", "react-shadow": "^20.6.0", "scroll-into-view-if-needed": "^3.1.0", "styled-components": "^6.1.17", "throttle-debounce": "^5.0.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/compat": "^1.3.0", "@eslint/js": "^9.29.0", "@rspack/cli": "^1.4.4", "@rspack/core": "^1.4.4", "@rspack/plugin-react-refresh": "^1.4.3", "@svgr/webpack": "^8.1.0", "@swc/plugin-styled-components": "^8.0.2", "@types/lodash": "^4.17.20", "@types/react": "^19.1.7", "@types/react-dom": "^19.1.6", "cross-env": "^7.0.3", "eslint": "^9.29.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.2.0", "picocolors": "^1.1.1", "prettier": "^3.5.3", "prettier-plugin-css-order": "^2.1.2", "react-refresh": "^0.17.0", "simple-git-hooks": "^2.13.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1"}, "simple-git-hooks": {"pre-commit": "pnpm format && pnpm lint", "commit-msg": "node scripts/verify-commit.js"}, "engines": {"node": ">=16", "pnpm": ">=7"}}